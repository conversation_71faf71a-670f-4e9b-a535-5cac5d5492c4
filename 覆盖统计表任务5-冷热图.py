# -*- coding: utf-8 -*-
"""
Created on Mon Sep  1 10:23:08 2025

@author: wxj01
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.collections as mcollections
import glob
import os
import numpy as np
import gc

# 设置matplotlib后端为非交互式
import matplotlib

# 设置文件路径 - 使用当前目录
path1 = r'C:\Users\<USER>\Downloads\hexagon_poi_details\hexagon_poi_details_first_500'
csv_files = glob.glob(os.path.join(path1, "*.csv"))

# 设置文件路径 - 使用当前目录
path2 = r'C:\Users\<USER>\Downloads\hexagon_poi_details\hexagon_user_counts_bj_500'
popu_csv_files = glob.glob(os.path.join(path2, "*.csv"))

# 指定的POI类型
poi_types = ['文化教育', '亲子', '美食', '休闲娱乐', '医疗', '购物', '运动健身', 
             '旅游景点', '金融', '丽人', '交通设施', '酒店', '生活服务', '办公住宅']
# 读取所有CSV文件
dfs = []
for file in csv_files:
    try:
        df0 = pd.read_csv(file)
        dfs.append(df0)
    except Exception as e:
        print(f"Error reading {file}: {e}")

if not dfs:
    print("No valid CSV files were read.")
    exit()

# 合并所有数据
df = pd.concat(dfs, ignore_index=True)

# 读取所有CSV文件
dfs = []
for file in popu_csv_files:
    try:
        df0 = pd.read_csv(file)
        dfs.append(df0)
    except Exception as e:
        print(f"Error reading {file}: {e}")

if not dfs:
    print("No valid CSV files were read.")
    exit()

# 合并所有数据
df_pop = pd.concat(dfs, ignore_index=True)
df_pop=df_pop.drop(columns=['grid_lat','grid_lon'])
df_pop=df_pop.rename(columns={'count':'pop'})


df = df[df['poi_type'].isin(poi_types)]

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
import seaborn as sns
from scipy import stats
from pysal.lib import weights
from esda.moran import Moran
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


# ================================================================================
# 任务1: JOIN操作，保留df中相同名称的列
# ================================================================================
print("正在执行任务1: JOIN操作...")

# 执行join操作，suffixes参数确保df的列优先保留
df = pd.merge(df, df_pop, on='grid_id', how='inner')
# ================================================================================
# 任务5: Gi*热点冷点分析（六边形展示）
# ================================================================================
print("正在执行任务5: Gi*热点冷点分析...")

from esda.getisord import G_Local
import matplotlib.patches as patches
df=df.drop(columns=['grid_id'])
# 计算六边形参数
diagonal_meters = 500
r_meters = diagonal_meters / 2
lat = 39.9  # 假设纬度，可根据实际数据调整
meters_per_degree_lat = 111000
meters_per_degree_lon = 111000 * np.cos(np.radians(lat))
r_degrees_lat = r_meters / meters_per_degree_lat
r_degrees_lon = r_meters / meters_per_degree_lon

def create_hexagon_vertices(center_lon, center_lat, r_lon, r_lat):
    """创建六边形顶点"""
    angles = np.linspace(np.pi/6, 13 * np.pi/6, 7)
    x = center_lon + r_lon * np.cos(angles)
    y = center_lat + r_lat * np.sin(angles)
    return np.column_stack((x, y))

def classify_hotspots(gi_values, p_values, alpha=0.05):
    """
    根据Gi*统计量和p值对热点冷点进行分类
    返回分类标签：
    0: 不显著
    1: 热点（99%置信度）
    2: 热点（95%置信度）  
    3: 热点（90%置信度）
    -1: 冷点（99%置信度）
    -2: 冷点（95%置信度）
    -3: 冷点（90%置信度）
    """
    classification = np.zeros(len(gi_values))
    
    for i in range(len(gi_values)):
        gi = gi_values[i]
        p = p_values[i]
        
        if p <= 0.01:  # 99%置信度
            classification[i] = 1 if gi > 0 else -1
        elif p <= 0.05:  # 95%置信度
            classification[i] = 2 if gi > 0 else -2
        elif p <= 0.10:  # 90%置信度
            classification[i] = 3 if gi > 0 else -3
        else:
            classification[i] = 0  # 不显著
    
    return classification

def get_hotspot_colors():
    """定义热点冷点的颜色映射"""
    colors = {
        1: '#8B0000',   # 深红色 - 热点99%
        2: '#FF0000',   # 红色 - 热点95%
        3: '#FF6666',   # 浅红色 - 热点90%
        0: '#FFFFFF',   # 白色 - 不显著
        -3: '#6666FF',  # 浅蓝色 - 冷点90%
        -2: '#0000FF',  # 蓝色 - 冷点95%
        -1: '#000080'   # 深蓝色 - 冷点99%
    }
    return colors

def get_hotspot_labels():
    """定义热点冷点的标签"""
    labels = {
        1: '热点 (99%)',
        2: '热点 (95%)',
        3: '热点 (90%)',
        0: '不显著',
        -3: '冷点 (90%)',
        -2: '冷点 (95%)',
        -1: '冷点 (99%)'
    }
    return labels

for i, poi in enumerate(poi_types):
    poi_data = df[df['poi_type'] == poi].copy()
    poi_data=poi_data.drop(columns=['poi_type'])
    if len(poi_data) < 4:  # Gi*分析需要足够的空间单元
        print(f"跳过{poi}: 数据点不足")
        continue
    
    # 创建子图用于count和min_distance的Gi*分析
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24, 10))

    # 构建空间权重矩阵
    coords = poi_data[['grid_lon', 'grid_lat']].values
    k_neighbors = min(8, len(coords)-1)
    w = weights.KNN.from_array(coords, k=k_neighbors)
    
    # 行标准化权重矩阵 - 处理版本兼容性
    try:
        # 尝试新版本的方法
        w.transform = 'r'
        # 确保transformation属性存在（兼容性）
        if not hasattr(w, 'transformation'):
            w.transformation = 'r'
    except:
        # 如果新方法失败，尝试旧版本的方法
        try:
            w.transformation = 'r'
        except:
            # 如果都失败了，创建行标准化的权重矩阵
            w = w.transform('r')
            if not hasattr(w, 'transformation'):
                w.transformation = 'r'
    
    # ======================= Count的Gi*分析 =======================
    count_values = poi_data['count'].values
    
    # 计算Gi*统计量 - 使用更兼容的参数设置
    try:
        gi_count = G_Local(count_values, w, star=True, permutations=999)
    except Exception as e:
        print(f"尝试使用默认参数重新计算Gi*统计量: {e}")
        # 如果有问题，尝试不指定transform参数
        gi_count = G_Local(count_values, w, star=True, permutations=999, transform='r')
    
    # 对热点冷点进行分类
    count_classification = classify_hotspots(gi_count.Gs, gi_count.p_sim)
    
    # 绘制六边形热点冷点图
    centers = coords
    colors = get_hotspot_colors()
    labels = get_hotspot_labels()
    
    # 创建六边形并按分类着色
    for j, (center, classification) in enumerate(zip(centers, count_classification)):
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        hex_patch = patches.Polygon(hex_verts, facecolor=colors[classification], 
                                  edgecolor='black', linewidth=0.5, alpha=0.8)
        ax1.add_patch(hex_patch)
    
    # 设置坐标轴
    ax1.set_xlim(centers[:, 0].min() - 0.01, centers[:, 0].max() + 0.01)
    ax1.set_ylim(centers[:, 1].min() - 0.01, centers[:, 1].max() + 0.01)
    ax1.set_aspect('equal')
    ax1.set_title(f'{poi} - Count Gi*热点冷点分析\n(Gi*统计量范围: {gi_count.Gs.min():.3f} ~ {gi_count.Gs.max():.3f})', 
                 fontsize=14, pad=20)
    ax1.set_xlabel('经度', fontsize=12)
    ax1.set_ylabel('纬度', fontsize=12)
    
    # 创建图例
    legend_elements = []
    unique_classes = np.unique(count_classification)
    for cls in unique_classes:
        legend_elements.append(patches.Patch(facecolor=colors[cls], 
                                           edgecolor='black', 
                                           label=labels[cls]))
    ax1.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1))
    
    # 添加统计信息
    hot_99 = np.sum(count_classification == 1)
    hot_95 = np.sum(count_classification == 2)
    hot_90 = np.sum(count_classification == 3)
    cold_99 = np.sum(count_classification == -1)
    cold_95 = np.sum(count_classification == -2)
    cold_90 = np.sum(count_classification == -3)
    not_sig = np.sum(count_classification == 0)
    
    stats_text = f"统计摘要:\n热点99%: {hot_99}\n热点95%: {hot_95}\n热点90%: {hot_90}\n"
    stats_text += f"冷点99%: {cold_99}\n冷点95%: {cold_95}\n冷点90%: {cold_90}\n不显著: {not_sig}"
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # ======================= Min_distance的Gi*分析 =======================
    distance_values = poi_data['min_distance'].values
    
    # 计算Gi*统计量 - 使用更兼容的参数设置
    try:
        gi_distance = G_Local(distance_values, w, star=True, permutations=999)
    except Exception as e:
        print(f"尝试使用默认参数重新计算Gi*统计量: {e}")
        # 如果有问题，尝试不指定transform参数
        gi_distance = G_Local(distance_values, w, star=True, permutations=999, transform='r')
    
    # 对热点冷点进行分类
    distance_classification = classify_hotspots(gi_distance.Gs, gi_distance.p_sim)
    
    # 绘制六边形热点冷点图
    for j, (center, classification) in enumerate(zip(centers, distance_classification)):
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        hex_patch = patches.Polygon(hex_verts, facecolor=colors[classification], 
                                  edgecolor='black', linewidth=0.5, alpha=0.8)
        ax2.add_patch(hex_patch)
    
    # 设置坐标轴
    ax2.set_xlim(centers[:, 0].min() - 0.01, centers[:, 0].max() + 0.01)
    ax2.set_ylim(centers[:, 1].min() - 0.01, centers[:, 1].max() + 0.01)
    ax2.set_aspect('equal')
    ax2.set_title(f'{poi} - Min Distance Gi*热点冷点分析\n(Gi*统计量范围: {gi_distance.Gs.min():.3f} ~ {gi_distance.Gs.max():.3f})', 
                 fontsize=14, pad=20)
    ax2.set_xlabel('经度', fontsize=12)
    ax2.set_ylabel('纬度', fontsize12)
    
    # 创建图例
    legend_elements = []
    unique_classes = np.unique(distance_classification)
    for cls in unique_classes:
        legend_elements.append(patches.Patch(facecolor=colors[cls], 
                                           edgecolor='black', 
                                           label=labels[cls]))
    ax2.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1))
    
    # 添加统计信息
    hot_99 = np.sum(distance_classification == 1)
    hot_95 = np.sum(distance_classification == 2)
    hot_90 = np.sum(distance_classification == 3)
    cold_99 = np.sum(distance_classification == -1)
    cold_95 = np.sum(distance_classification == -2)
    cold_90 = np.sum(distance_classification == -3)
    not_sig = np.sum(distance_classification == 0)
    
    stats_text = f"统计摘要:\n热点99%: {hot_99}\n热点95%: {hot_95}\n热点90%: {hot_90}\n"
    stats_text += f"冷点99%: {cold_99}\n冷点95%: {cold_95}\n冷点90%: {cold_90}\n不显著: {not_sig}"
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(f'{poi}_gi_star_hotspot_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"{poi} Gi*分析完成")
    
    # 保存详细的Gi*统计结果到CSV
    gi_results = pd.DataFrame({
        'grid_lon': poi_data['grid_lon'].values,
        'grid_lat': poi_data['grid_lat'].values,
        'count': poi_data['count'].values,
        'min_distance': poi_data['min_distance'].values,
        'count_gi_star': gi_count.Gs,
        'count_p_value': gi_count.p_sim,
        'count_classification': count_classification,
        'distance_gi_star': gi_distance.Gs,
        'distance_p_value': gi_distance.p_sim,
        'distance_classification': distance_classification
    })
    
    gi_results.to_csv(f'{poi}_gi_star_results.csv', index=False, encoding='utf-8-sig')