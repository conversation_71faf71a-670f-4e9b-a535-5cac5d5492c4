# -*- coding: utf-8 -*-
"""
Created on Mon Sep  1 10:00:23 2025

@author: wxj01
"""

# -*- coding: utf-8 -*-
"""
Hexagonal Grid Visualization for POI Data
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.collections as mcollections
import glob
import os
import numpy as np
import gc

# 设置matplotlib后端为非交互式
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置文件路径 - 使用当前目录

file = r'C:\Users\<USER>\Downloads\hexagon_poi_details\hexagon_poi_details_first_500\first_500_full_gen.csv'



# 合并所有数据
df = pd.read_csv(file)


# 过滤经纬度范围：lon在116-117；lat在39.6-40.4
df = df[(df['grid_lon'] >= 116) & (df['grid_lon'] <= 117) &
        (df['grid_lat'] >= 39.6) & (df['grid_lat'] <= 40.4)]
print(f"Data points after geographic filtering: {len(df)}")

# 数据采样设置
USE_SAMPLE = False  # 设置为False以使用完整数据集
SAMPLE_FRACTION = 0.001  # 采样比例 (0.001 = 0.1%)

if USE_SAMPLE:
    df = df.sample(frac=SAMPLE_FRACTION, random_state=42)
    print(f"Using sample data: {len(df)} data points ({SAMPLE_FRACTION*100}% of filtered data)")
else:
    print(f"Using full dataset: {len(df)} data points")

# 计算六边形参数
diagonal_meters = 500
r_meters = diagonal_meters / 2
lat = 39.9
meters_per_degree_lat = 111000
meters_per_degree_lon = 111000 * np.cos(np.radians(lat))
r_degrees_lat = r_meters / meters_per_degree_lat
r_degrees_lon = r_meters / meters_per_degree_lon

def create_hexagon_vertices(center_lon, center_lat, r_lon, r_lat):
    """创建六边形顶点"""
    angles = np.linspace(np.pi/6, 13 * np.pi/6, 7)
    x = center_lon + r_lon * np.cos(angles)
    y = center_lat + r_lat * np.sin(angles)
    return np.column_stack((x, y))

# 创建颜色映射
cmap_count = mcolors.LinearSegmentedColormap.from_list("", ["white", "#0021DA"])
cmap_distance = mcolors.LinearSegmentedColormap.from_list("", ["#00B646", "white"])

# 为每种POI单独作图：Count数据

poi_data = df


# 创建单独的图
fig_count, ax_count = plt.subplots(1, 1, figsize=(20, 16))

# 准备数据
centers = poi_data[['grid_lon', 'grid_lat']].values
values = poi_data['count'].values

# 创建六边形（无边界）
verts = []
for center in centers:
    hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
    verts.append(hex_verts)

# 归一化
max_count = np.max(values) if len(values) > 0 else 1
norm = mcolors.Normalize(vmin=0, vmax=max_count)

# 创建多边形集合（无边界）
pc = mcollections.PolyCollection(
    verts, array=values, cmap=cmap_count, norm=norm,
    edgecolors='none', linewidths=0
)

ax_count.add_collection(pc)
ax_count.set_title(f'full - Count Data', fontsize=16)
ax_count.set_xlabel('Longitude', fontsize=14)
ax_count.set_ylabel('Latitude', fontsize=14)
ax_count.set_aspect('equal')

# 设置固定的坐标轴范围
ax_count.set_xlim(116, 117)
ax_count.set_ylim(39.6, 40.4)

# 添加颜色条
cbar = fig_count.colorbar(pc, ax=ax_count, label='Count')
cbar.ax.tick_params(labelsize=12)

# 保存单独的图
filename = f'hexagon_count_full_individual.png'
fig_count.savefig(filename, dpi=150, bbox_inches='tight')
plt.close(fig_count)
print(f"Saved: {filename}")

gc.collect()  # 清理内存

# 为每种POI单独作图：Distance数据
print("Creating individual distance visualizations...")

poi_data = df.copy()

# 创建单独的图
fig_distance, ax_distance = plt.subplots(1, 1, figsize=(20, 16))

# 转换距离数据
poi_data['adjusted_distance'] = poi_data['min_distance']

# 准备数据
centers = poi_data[['grid_lon', 'grid_lat']].values
values = poi_data['adjusted_distance'].values

# 创建六边形（无边界）
verts = []
for center in centers:
    hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
    verts.append(hex_verts)

# 归一化
max_distance = np.max(values) if len(values) > 0 else 1
norm = mcolors.Normalize(vmin=0, vmax=max_distance)

# 创建多边形集合（无边界）
pc = mcollections.PolyCollection(
    verts, array=values, cmap=cmap_distance, norm=norm,
    edgecolors='none', linewidths=0
)

ax_distance.add_collection(pc)
ax_distance.set_title(f'full - Distance Data', fontsize=16)
ax_distance.set_xlabel('Longitude', fontsize=14)
ax_distance.set_ylabel('Latitude', fontsize=14)
ax_distance.set_aspect('equal')

# 设置固定的坐标轴范围
ax_distance.set_xlim(116, 117)
ax_distance.set_ylim(39.6, 40.4)

# 添加颜色条
cbar = fig_distance.colorbar(pc, ax=ax_distance, label='Adjusted Distance (m)')
cbar.ax.tick_params(labelsize=12)

# 保存单独的图
filename = f'hexagon_distance_full_individual.png'
fig_distance.savefig(filename, dpi=150, bbox_inches='tight')
plt.close(fig_distance)
print(f"Saved: {filename}")

gc.collect()  # 清理内存
