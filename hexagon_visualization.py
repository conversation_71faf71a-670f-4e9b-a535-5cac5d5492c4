# -*- coding: utf-8 -*-
"""
Hexagonal Grid Visualization for POI Data
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.collections as mcollections
import glob
import os
import numpy as np
import gc

# 设置matplotlib后端为非交互式
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置文件路径 - 使用当前目录
path = '.'
csv_files = glob.glob(os.path.join(path, "*.csv"))

# 指定的POI类型
poi_types = ['文化教育', '亲子', '美食', '休闲娱乐', '医疗', '购物', '运动健身', 
             '旅游景点', '金融', '丽人', '交通设施', '酒店', '生活服务', '办公住宅']

# 读取所有CSV文件
dfs = []
for file in csv_files:
    try:
        df0 = pd.read_csv(file)
        dfs.append(df0)
    except Exception as e:
        print(f"Error reading {file}: {e}")

if not dfs:
    print("No valid CSV files were read.")
    exit()

# 合并所有数据
df = pd.concat(dfs, ignore_index=True)

# 检查实际的POI类型
print("Actual POI types in data:")
print(df['poi_type'].unique())
print(f"Total unique POI types: {len(df['poi_type'].unique())}")

# 过滤POI类型
df = df[df['poi_type'].isin(poi_types)]
print(f"Data points after filtering: {len(df)}")

# 过滤经纬度范围：lon在116-117；lat在39.6-40.4
df = df[(df['grid_lon'] >= 116) & (df['grid_lon'] <= 117) &
        (df['grid_lat'] >= 39.6) & (df['grid_lat'] <= 40.4)]
print(f"Data points after geographic filtering: {len(df)}")

# 数据采样设置
USE_SAMPLE = False  # 设置为False以使用完整数据集
SAMPLE_FRACTION = 0.001  # 采样比例 (0.001 = 0.1%)

if USE_SAMPLE:
    df = df.sample(frac=SAMPLE_FRACTION, random_state=42)
    print(f"Using sample data: {len(df)} data points ({SAMPLE_FRACTION*100}% of filtered data)")
else:
    print(f"Using full dataset: {len(df)} data points")

# 计算六边形参数
diagonal_meters = 500
r_meters = diagonal_meters / 2
lat = 39.9
meters_per_degree_lat = 111000
meters_per_degree_lon = 111000 * np.cos(np.radians(lat))
r_degrees_lat = r_meters / meters_per_degree_lat
r_degrees_lon = r_meters / meters_per_degree_lon

def create_hexagon_vertices(center_lon, center_lat, r_lon, r_lat):
    """创建六边形顶点"""
    angles = np.linspace(np.pi/6, 13 * np.pi/6, 7)
    x = center_lon + r_lon * np.cos(angles)
    y = center_lat + r_lat * np.sin(angles)
    return np.column_stack((x, y))

# 创建颜色映射
cmap_count = mcolors.LinearSegmentedColormap.from_list("", ["white", "#0021DA"])
cmap_distance = mcolors.LinearSegmentedColormap.from_list("", ["#00B646", "white"])

# 为每种POI单独作图：Count数据
print("Creating individual count visualizations...")
for i, poi in enumerate(poi_types):
    print(f"Processing count data for {poi} ({i+1}/{len(poi_types)})")
    poi_data = df[df['poi_type'] == poi]

    if poi_data.empty:
        print(f"No data for {poi}, skipping...")
        continue

    # 创建单独的图
    fig_count, ax_count = plt.subplots(1, 1, figsize=(20, 16))

    # 准备数据
    centers = poi_data[['grid_lon', 'grid_lat']].values
    values = poi_data['count'].values

    # 创建六边形（无边界）
    verts = []
    for center in centers:
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        verts.append(hex_verts)

    # 归一化
    max_count = np.max(values) if len(values) > 0 else 1
    norm = mcolors.Normalize(vmin=0, vmax=max_count)

    # 创建多边形集合（无边界）
    pc = mcollections.PolyCollection(
        verts, array=values, cmap=cmap_count, norm=norm,
        edgecolors='none', linewidths=0
    )

    ax_count.add_collection(pc)
    ax_count.set_title(f'{poi} - Count Data', fontsize=16)
    ax_count.set_xlabel('Longitude', fontsize=14)
    ax_count.set_ylabel('Latitude', fontsize=14)
    ax_count.set_aspect('equal')

    # 设置固定的坐标轴范围
    ax_count.set_xlim(116, 117)
    ax_count.set_ylim(39.6, 40.4)

    # 添加颜色条
    cbar = fig_count.colorbar(pc, ax=ax_count, label='Count')
    cbar.ax.tick_params(labelsize=12)

    # 保存单独的图
    filename = f'hexagon_count_{poi}_individual.png'
    fig_count.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close(fig_count)
    print(f"Saved: {filename}")

gc.collect()  # 清理内存

# 为每种POI单独作图：Distance数据
print("Creating individual distance visualizations...")
for i, poi in enumerate(poi_types):
    print(f"Processing distance data for {poi} ({i+1}/{len(poi_types)})")
    poi_data = df[df['poi_type'] == poi].copy()

    if poi_data.empty:
        print(f"No data for {poi}, skipping...")
        continue

    # 创建单独的图
    fig_distance, ax_distance = plt.subplots(1, 1, figsize=(20, 16))

    # 转换距离数据
    poi_data['adjusted_distance'] = poi_data['min_distance'].apply(lambda x: max(0, x - 150))

    # 准备数据
    centers = poi_data[['grid_lon', 'grid_lat']].values
    values = poi_data['adjusted_distance'].values

    # 创建六边形（无边界）
    verts = []
    for center in centers:
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        verts.append(hex_verts)

    # 归一化
    max_distance = np.max(values) if len(values) > 0 else 1
    norm = mcolors.Normalize(vmin=0, vmax=max_distance)

    # 创建多边形集合（无边界）
    pc = mcollections.PolyCollection(
        verts, array=values, cmap=cmap_distance, norm=norm,
        edgecolors='none', linewidths=0
    )

    ax_distance.add_collection(pc)
    ax_distance.set_title(f'{poi} - Distance Data', fontsize=16)
    ax_distance.set_xlabel('Longitude', fontsize=14)
    ax_distance.set_ylabel('Latitude', fontsize=14)
    ax_distance.set_aspect('equal')

    # 设置固定的坐标轴范围
    ax_distance.set_xlim(116, 117)
    ax_distance.set_ylim(39.6, 40.4)

    # 添加颜色条
    cbar = fig_distance.colorbar(pc, ax=ax_distance, label='Adjusted Distance (m)')
    cbar.ax.tick_params(labelsize=12)

    # 保存单独的图
    filename = f'hexagon_distance_{poi}_individual.png'
    fig_distance.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close(fig_distance)
    print(f"Saved: {filename}")

gc.collect()  # 清理内存

# 读取用户计数数据并可视化
print("Processing user count data...")
user_count_path = r'..\hexagon_user_counts_bj_500'
user_csv_files = glob.glob(os.path.join(user_count_path, "*.csv"))

user_dfs = []
for file in user_csv_files:
    try:
        df_user = pd.read_csv(file)
        user_dfs.append(df_user)
    except Exception as e:
        print(f"Error reading {file}: {e}")

if user_dfs:
    # 合并用户数据
    df_user = pd.concat(user_dfs, ignore_index=True)
    print(f"Total user count data points: {len(df_user)}")

    # 过滤经纬度范围
    df_user = df_user[(df_user['grid_lon'] >= 116) & (df_user['grid_lon'] <= 117) &
                      (df_user['grid_lat'] >= 39.6) & (df_user['grid_lat'] <= 40.4)]
    print(f"User count data points after geographic filtering: {len(df_user)}")

    # 创建用户计数可视化
    fig_user, ax_user = plt.subplots(1, 1, figsize=(20, 16))

    # 准备数据
    centers = df_user[['grid_lon', 'grid_lat']].values
    values = df_user['count'].values

    # 创建六边形（无边界）
    verts = []
    for center in centers:
        hex_verts = create_hexagon_vertices(center[0], center[1], r_degrees_lon, r_degrees_lat)
        verts.append(hex_verts)

    # 归一化
    max_user_count = np.max(values) if len(values) > 0 else 1
    norm = mcolors.Normalize(vmin=0, vmax=max_user_count)

    # 创建多边形集合（无边界）
    pc = mcollections.PolyCollection(
        verts, array=values, cmap=cmap_count, norm=norm,
        edgecolors='none', linewidths=0
    )

    ax_user.add_collection(pc)
    ax_user.set_title('User Count Data', fontsize=16)
    ax_user.set_xlabel('Longitude', fontsize=14)
    ax_user.set_ylabel('Latitude', fontsize=14)
    ax_user.set_aspect('equal')

    # 设置固定的坐标轴范围
    ax_user.set_xlim(116, 117)
    ax_user.set_ylim(39.6, 40.4)

    # 添加颜色条
    cbar = fig_user.colorbar(pc, ax=ax_user, label='User Count')
    cbar.ax.tick_params(labelsize=12)

    # 保存用户计数图
    user_filename = 'hexagon_user_count.png'
    fig_user.savefig(user_filename, dpi=150, bbox_inches='tight')
    plt.close(fig_user)
    print(f"Saved: {user_filename}")
else:
    print("No user count data files found or readable.")

print(f"Processed {len(df)} POI data points")
if USE_SAMPLE:
    print(f"Visualization complete with {SAMPLE_FRACTION*100}% sample.")
    print("To use the full dataset, set USE_SAMPLE = False in the script.")
    print("Warning: Full dataset processing may take significant time and memory.")
else:
    print("Visualization complete with full dataset.")

print("Individual visualizations created for each POI type and user count data.")
print("All hexagons rendered without borders and constrained to lon: 116-117, lat: 39.6-40.4")